//
//  DanmakuTextCell.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Example text danmaku cell
//

import Cocoa

public class DanmakuTextCell: DanmakuCell {
    
    public override func willDisplay() {
        super.willDisplay()
    }
    
    public override func displaying(_ context: CGContext, _ size: CGSize, _ isCancelled: Bool) {
        guard !isCancelled, let model = model as? DanmakuTextCellModel else { return }

        let text = NSString(string: model.text)

        // 设置文字绘制参数，类似iOS版本
        context.setLineWidth(max(0.5, model.strokeWidth))
        context.setLineJoin(.round)
        context.setLineCap(.round)

        // 计算文字位置，垂直居中
        let attributes: [NSAttributedString.Key: Any] = [.font: model.font]
        let textSize = text.boundingRect(with: CGSize(width: CGFloat.greatestFiniteMagnitude, height: CGFloat.greatestFiniteMagnitude),
                                       options: [.usesLineFragmentOrigin, .usesFontLeading],
                                       attributes: attributes,
                                       context: nil).size

        let inset: CGFloat = max(1, model.strokeWidth)
        let drawPoint = CGPoint(x: inset, y: max(inset, (size.height - textSize.height) / 2.0))

        // 先绘制描边（如果启用）
        if model.strokeWidth > 0 {
            context.saveGState()
            context.setStrokeColor(model.strokeColor.cgColor)
            context.setTextDrawingMode(.stroke)
            let strokeAttributes: [NSAttributedString.Key: Any] = [
                .font: model.font,
                .foregroundColor: model.strokeColor
            ]
            text.draw(at: drawPoint, withAttributes: strokeAttributes)
            context.restoreGState()
        }

        // 再绘制填充文字
        context.setTextDrawingMode(.fill)
        let fillAttributes: [NSAttributedString.Key: Any] = [
            .font: model.font,
            .foregroundColor: model.textColor
        ]
        text.draw(at: drawPoint, withAttributes: fillAttributes)
    }
    
    public override func didDisplay(_ finished: Bool) {
        super.didDisplay(finished)
    }
    
}
