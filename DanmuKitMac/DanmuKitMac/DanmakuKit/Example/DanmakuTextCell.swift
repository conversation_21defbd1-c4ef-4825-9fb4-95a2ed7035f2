//
//  DanmakuTextCell.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Example text danmaku cell
//

import Cocoa

public class DanmakuTextCell: DanmakuCell {
    
    public override func willDisplay() {
        super.willDisplay()
    }
    
    public override func displaying(_ context: CGContext, _ size: CGSize, _ isCancelled: Bool) {
        guard !isCancelled, let model = model as? DanmakuTextCellModel else { return }

        // Use non-flipped NSGraphicsContext to avoid mirrored/upside-down text
        NSGraphicsContext.saveGraphicsState()
        let nsContext = NSGraphicsContext(cgContext: context, flipped: false)
        NSGraphicsContext.current = nsContext
        defer { NSGraphicsContext.restoreGraphicsState() }

        // Compose attributed string with stroke (outline) and fill
        let attributes: [NSAttributedString.Key: Any] = [
            .font: model.font,
            .foregroundColor: model.textColor,
            .strokeColor: model.strokeColor,
            .strokeWidth: -max(0.5, model.strokeWidth) // negative = draw stroke + fill
        ]
        let attributed = NSAttributedString(string: model.text, attributes: attributes)

        // Prepare shadow if enabled
        if model.enableShadow {
            let shadow = NSShadow()
            shadow.shadowColor = model.shadowColor
            shadow.shadowOffset = model.shadowOffset
            shadow.shadowBlurRadius = model.shadowBlurRadius
            shadow.set()
        }

        // Compute text size and draw centered vertically from bottom-left coordinate system
        let textBounds = attributed.boundingRect(with: CGSize(width: size.width, height: CGFloat.greatestFiniteMagnitude), options: [.usesLineFragmentOrigin, .usesFontLeading])
        let inset: CGFloat = max(1, abs(model.strokeWidth))
        let textHeight = ceil(textBounds.height)
        let originY = max(inset, floor((size.height - textHeight) / 2.0))
        let drawRect = CGRect(x: inset, y: originY, width: max(0, size.width - inset * 2), height: textHeight)
        attributed.draw(in: drawRect)
    }
    
    public override func didDisplay(_ finished: Bool) {
        super.didDisplay(finished)
    }
    
}
