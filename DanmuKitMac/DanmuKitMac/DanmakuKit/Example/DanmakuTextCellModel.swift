//
//  DanmakuTextCellModel.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Example text danmaku model
//

import Cocoa

public class DanmakuTextCellModel: DanmakuCellModel {
    
    public var identifier: String = ""
    
    public var text: String = ""
    
    public var font: NSFont = NSFont.systemFont(ofSize: 15)
    
    /// 文本颜色（默认白色）
    public var textColor: NSColor = NSColor.white

    /// 描边颜色（默认黑色，提升在浅色背景上的可读性）
    public var strokeColor: NSColor = NSColor.black

    /// 描边宽度，负值表示“描边+填充”，建议 1.0~2.0
    public var strokeWidth: CGFloat = 1.5

    /// 阴影颜色（默认黑色 60% 透明度）
    public var shadowColor: NSColor = NSColor.black.withAlphaComponent(0.6)

    /// 阴影偏移（默认向下 1px）
    public var shadowOffset: CGSize = CGSize(width: 0, height: -1)

    /// 阴影模糊半径（默认 2）
    public var shadowBlurRadius: CGFloat = 2.0

    /// 是否启用阴影（默认启用）
    public var enableShadow: Bool = true
    
    public var size: CGSize = .zero
    
    public var track: UInt?
    
    public var displayTime: Double = 8.0
    
    public var type: DanmakuCellType = .floating
    
    public var cellClass: DanmakuCell.Type {
        return DanmakuTextCell.self
    }
    
    public init() {}
    
    public init(text: String) {
        self.text = text
        self.identifier = UUID().uuidString
        calculateSize()
    }
    
    public func calculateSize() {
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .strokeWidth: -max(0.5, strokeWidth)
        ]

        let attributedString = NSAttributedString(string: text, attributes: attributes)
        let boundingRect = attributedString.boundingRect(
            with: CGSize(width: CGFloat.greatestFiniteMagnitude, height: CGFloat.greatestFiniteMagnitude),
            options: [.usesLineFragmentOrigin, .usesFontLeading]
        )

        // Extra padding for stroke + optional shadow
        let inset = max(1, abs(strokeWidth))
        let shadowPadX = enableShadow ? (abs(shadowOffset.width) + shadowBlurRadius) : 0
        let shadowPadY = enableShadow ? (abs(shadowOffset.height) + shadowBlurRadius) : 0
        size = CGSize(width: ceil(boundingRect.width) + inset * 2 + shadowPadX,
                      height: ceil(boundingRect.height) + inset * 2 + shadowPadY)
    }
    
    public func isEqual(to cellModel: DanmakuCellModel) -> Bool {
        return identifier == cellModel.identifier
    }
    
}
